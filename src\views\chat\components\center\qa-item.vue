<template>
  <div class="w-full qa-item">
    <div v-if="!item.sensitive" class="question">
      <a-tooltip title="复制">
        <div class="question-content" @click="doCopy()">{{ item.question }}</div>
      </a-tooltip>
    </div>

    <deepseek :text="thinkTxt" :item="item" :loading="thinkLoading" @open-quote="openQuote" />

    <ai-search
      :text="searchTxt"
      :loading="searchLoading"
      @open-quote-search="openQuoteSearch"
    ></ai-search>

    <Spin v-if="answerLoading" size="small" class="qa-loading" />
    <template v-else>
      <div class="answer">
        <div class="w-[100%] mt-[4px]">
          <div v-if="showAnswer" class="content" :class="{'content-illegal':item.sensitive,'content-error':item.isError,'content-stop':item.isTerminate}">
            <div class="tip">{{ systemInfo.answerTip }}</div>
            <MdText v-if="mdText" :text="mdText" :is-show-quote="isShowQuote" @open-quote="openQuote" />
            <span v-if="item.isError">网络出了个小差，请刷新网页</span>
            <!-- 内嵌版本 -->
            <quote-card ref="quoteCardRef" v-model:visible="quoteCardVisible" />
          </div>
          <toolbar :answer="item" :index="index"/>
        </div>
        <!-- 有且只有一个问答为敏感词  -->
        <a-flex v-if="item.sensitive && state.qaList.length === 1" class="new-chat">
          <span class="t1">点击</span>
          <span class="t1 t2" @click="openNewChat">新建对话</span>
        </a-flex>
        <!-- 最后一个问答对有敏感词时出现倒计时 loop-->
        <Lottie v-show="isSensitive" :loop="false"  width="28px" height="28px" :json-data="CountDownJson" style="margin-left: auto" />
      </div>
    </template>
  </div>
</template>

<script setup lang='ts'>
import { computed,nextTick, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Spin, message } from 'ant-design-vue'
import { Lottie } from '@/components'
import { useMenusStore } from '@/store'
import { useChatGptInject } from '@/views/chat/hooks/use-chat'
import { copyToClip } from '@/utils/copy'
import { extractMap } from '@/utils/tools'
import CountDownJson from '@/assets/json/count-down.json'
import MdText from '@/views/chat/components/center/md-text.vue'
import Toolbar from '@/views/chat/components/center/toolbar.vue'
import QuoteCard from '@/views/chat-robot/components/quote-card/index.vue'
import Deepseek from '@/views/chat/components/center/deepseek.vue'
import AiSearch from '@/views/chat/components/center/ai-search.vue'
type Props = {
  item: Record<string, any>
  index?: number// 问答对索引，已有返回结果
  isShowQuote?: boolean// 是否显示引用按钮
  isChatRobot?:boolean // 是否内嵌页
}
const props = withDefaults(defineProps<Props>(), {
  isShowQuote: true,
  index: 0,
  isChatRobot: false
})
const emit = defineEmits(['openQuote','openQuoteSearch'])

const { state, resetChat} = useChatGptInject()

const isSensitive = computed(()=> props.index === state.qaList.length-1 && 1 < state.qaList.length && props.item.hasSensitiveStyle)
// 系统信息
const menusStore = useMenusStore()
const systemInfo = computed(() => menusStore.systemInfo)
const quoteCardVisible = ref(false)
const quoteCardRef = ref()
const answerLoading = computed(() => {
  const { loading } = props.item
  return (loading || mdText.value == '') && state.isLoading && props.index === state.qaList.length - 1
})
// 错误、网络开小差、终止
const showAnswer = computed(()=> {
  const { isError,isTerminate } = props.item
  return isError || !!(isTerminate && !thinkTxt.value) ||  !!mdText.value
})
// mdText
const mdText = computed(()=> {
  let value =  props.item.content
  if(!value) return ''
  if(value.trim() === '') return ''
  const forbiddenPatterns = ['<summary_ref_map>','</summary_ref_map>','<think>','</think>']
  value = value.replace(/<think>[\s\S]*?<\/think>/g, '')
  value = value.replace(/<summary_ref_map>[\s\S]*?<\/summary_ref_map>/g, '')
  if(forbiddenPatterns.some(pattern => value.includes(pattern))) return ''
  else return value.trim()
})
//  think内容
const thinkTxt = computed(()=> {
  return extractMap(props.item.content,'<think>','</think>')
})
const thinkLoading = computed(()=> {
  const { content='', isTerminate } =  props.item
  let loading = false
  if(content && content.includes('<think>') && !content.includes('</think>')) loading = true
  else if(content && content.includes('</think>')) loading = false
  return loading && !isTerminate
})
//  检索内容
const searchTxt = computed(()=> {
  return extractMap(props.item.content,'<summary_ref_map>','</summary_ref_map>')
})
const searchLoading = computed(()=> {
  const { content='', isTerminate } =  props.item
  let loading = false
  if(content && content.includes('<summary_ref_map>') && !content.includes('</summary_ref_map>')) loading = true
  else if(content && content.includes('</summary_ref_map>')) loading = false
  return loading && !isTerminate
})
// 引用
const openQuote = (dataVal: string)=> {
  if(props.isChatRobot) {
    state.selectTag = ''
    quoteCardOpen(dataVal)
    return
  }
  emit('openQuote', dataVal)
}
// 检索
const openQuoteSearch = (obj: Record<string,any>)=> {
  if(props.isChatRobot) {
    nextTick(() => {
      quoteCardRef.value?.initData(obj,true)
      if(quoteCardRef.value) quoteCardVisible.value = true
    })
    return
  }
  emit('openQuoteSearch', obj)
}
// 新建对话
const router = useRouter()
const openNewChat = ()=> {
  resetChat()
  state.isNewChat = true
  if(props.isChatRobot) router.replace({ name: 'ChatRobot' })
  else router.replace({name: 'ChatIndex' })
}
/**
 * 复制
 */
 function doCopy() {
  copyToClip(props.item.question)
    .then(() => {
      message.success('复制成功')
    })
    .catch(() => {
      message.error('复制失败')
    })
}

function quoteCardOpen(quoteIds: string) {
  quoteCardVisible.value = true
  nextTick(() => {
    quoteCardRef.value?.initData(quoteIds)
  })
}
</script>

<style lang="scss" scoped >
:deep(.ant-spin-spinning){
  text-align: left;
}
.qa-item {
  display: flex;
  flex-direction: column;
  gap: 32px;
  .question {
    .question-content {
      border-radius: var(--border-radius-16) var(--border-radius-2) var(--border-radius-16) var(--border-radius-16);
      padding: 16px;
      background: var(--main-1-3);
      color: var(--main-1-4);
      width: fit-content;
      margin-left: auto;
      white-space: break-spaces;
      cursor: pointer;
    }
  }
  .answer {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding-right: 12px;
    flex-grow: 1;
    .new-chat {
      height: 37px;
      align-items: center;
      padding: 4px 16px;
      gap: 4px;
      flex-grow: 1;
      justify-content: center;

      .t1 {
        font-family: 思源黑体;
        font-size: var(--font-20);
        font-weight: bold;
        line-height: normal;
        text-align: center;
        color: var(--text-2);
      }

      .t2 {
        color: var(--main-6);
        cursor: pointer;
      }
    }
    .content {
      position: relative;
      border-radius: var(--border-radius-2) var(--border-radius-12) var(--border-radius-12) var(--border-radius-12);
      padding: 24px 16px 16px 16px;
      background: var(--fill-1);
      width: 100%;
      min-width: 200px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .tip {
        position: absolute;
        right: 0;
        top: 0;
        border-radius: 0 var(--border-radius-8) 0  var(--border-radius-8);
        padding: 2px 12px;
        background: var(--main-1-1);
        font-size: var(--font-10);
        color: var(--main-1-2);
      }
      &.content-stop,
      &.content-error {
        width: fit-content;
      }
      &.content-illegal {
        background: var(--fill-1);
        border: 1px solid var(--error-6);

        .tip {
          right: 1px;
          top: 1px;
        }
      }
    }
  }
}
</style>
