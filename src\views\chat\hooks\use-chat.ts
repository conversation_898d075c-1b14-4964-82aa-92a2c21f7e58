import { reactive, provide, inject, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import  { useMenusStore } from '@/store'
import {  sseApiStopAnswerByEvents, apiGetHistorys, apiGetDislikeReasonList } from '@/api/chat'
import {  getCookie } from '@/utils/app-gateway'
import { useSse } from '@/views/chat/hooks/use-sse'

// 问答对显示
interface AnswerItem {
  questionId?: string// 问题id
  chatId?: string// 会话Id
  question?: string // 会话问题
  content?: string// 会话答案
  role?: 'assistant' | 'user'// 消息属性user为用户，assistant为AI回答
  answerId?: string// 答案id
  similarQuestionId?: string;// 联想id
  loading?: false// 是否加载答案中
  isFinish?: false // 完成加载
  thinkingTime?: string | null // 思考时间
  isRegenerate?: boolean // 是否重新生成
  type?: number// 0为没有任何状态，1点赞 2点踩
  sensitive?: boolean // true为违规，false为合规
  hasSensitiveStyle?: boolean // 是否有敏感样式
  isError?: boolean// 网络开小差
  isTerminate?: boolean // 是否中止回答字段
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  controller?: any // AbortController
  steps: any[]
}
const KEY_CHAT_INDEX_PROVIDE = 'AI_KEY_CHAT_INDEX_PROVIDE'
const KEY_CHAT_INDEX_PROVIDE_CURRENT_VIEW = 'AI_KEY_CHAT_CURRENT_VIEW'
// === 咨询问答 useChatGptProvide  ===
export const useChatGptProvide = () => {
  // 流格式 sensitive：true为违规，false为合规
  // {"questionId":"","chatId":"","content":"","answerId":"","sensitive":false}
  const initialState = {
    selectTag: '',// 选择检索标签
    qaList: [] as AnswerItem[],// 问答对
    reasonLoading: false,// 点踩理由loading
    dislikeReason: [], // 点踩理由
    originParams: {}, // 阅读原文参数
    associateInputList: [],// 联想列表
    inputVal: '',// 输入框的值
    question: '', // 当前问题
    questionId: '', // 当前问题id
    isRegenerate: false, // 是否重新生成
    chatId: '', // 会话id
    similarQuestionId: '',// 联想id
    isLoading: false, // 请求答案，还未有流式结果;（请求有结果，但是未进入close状态）
    isNewChat: true, // 是否新建会话
    isShowQuote:  false, // 是否显示引用依据
    isShowQuoteSearch:  false, // 是否显示检索
    isShowOriginal: false,// 是否显示原文
    disableAllInput: false // 是否禁止所有操作，非法输入时，禁止操作3秒
  }
  const state: any = reactive<Record<string, any>>(initialState)
  // 最后一条即当前条问答
  const currentQaMap = computed({
    get() {
      const index = state.qaList.length-1
      return state.qaList[index] ?? {}
    },
    set(val) {
      // 更新最后一条
      const index = state.qaList.length-1
      const lastItemAnswer = state.qaList[index]
      if(lastItemAnswer) state.qaList[index] = val ?? {}
      return val
    }
  })
  provide(KEY_CHAT_INDEX_PROVIDE, state)
  provide(KEY_CHAT_INDEX_PROVIDE_CURRENT_VIEW, currentQaMap)
  const methods = useChatGptMethods(state, currentQaMap)
  onBeforeUnmount(() => {
    state.qaList = []
    state.originParams = {}
    state.associateInputList = []
    state.selectTag = ''
    state.question = ''
    state.inputVal = ''
    state.similarQuestionId = ''
    state.questionId = ''
    state.isRegenerate = false
    state.reasonLoading = false
    state.chatId = ''
    state.isNewChat = true
    state.isLoading = false
    state.isShowQuote = false
    state.isShowQuoteSearch = false
    state.disableAllInput = false
    state.isShowOriginal = false
  })
  return { state, ...methods, currentQaMap  }
}

// === 咨询问答 useChatGptInject  ===
export const useChatGptInject = () => {
  const state: any = inject(KEY_CHAT_INDEX_PROVIDE)
  const currentQaMap: any = inject(KEY_CHAT_INDEX_PROVIDE_CURRENT_VIEW)
  const m = useChatGptMethods(state, currentQaMap)
  return { state, currentQaMap, ...m }
}

// === 咨询问答 useChatGptMethods  ===
const useChatGptMethods = (state: any, currentQaMap: any) => {
  const router = useRouter()
  /** 发送问题 */
  const sendQuestion = (content: string) => {
    if (!checkState()) return
    state.isLoading = true
    state.qaList.push({ loading: true, question: content, content: '', chatId: state.chatId,isFinish: false,thinkingTime: 0, steps: [] })
    const $options = dealSendParams(content)
    const { sseRequest,scrollToBottom } = useSse(state, currentQaMap)
    // 定位到会话最新位置
    scrollToBottom()
    sseRequest($options)
  }
  /** 处理发送参数 */
  const dealSendParams = (content: string)=> {
    // 不是新会话，取最新的5组聊天记录集
    let $params: any = null
    const qaList = state.qaList.slice(-5) ?? []
    if(state.isNewChat) {
      $params = {
        ...initSendParams(),
        query: content,
        messages: [{ role: 'user', content }]
      }
    } else {
      $params = {
        chatId: state.chatId ?? '',
        questionId: state.questionId ?? '',
        regeneration: state.isRegenerate,
        similarQuestionId: state.similarQuestionId ?? '',
        query: content,
        messages: []
      }
      qaList.map((item: Record<string,any>)=> {
        $params.messages.push({  role: 'user',  content: item.question })
        if(item.isFinish) $params.messages.push({  role: 'assistant',  content: item.content || ''})
      })
    }
    if(getCookie()?.isVisitor) {
      $params.tourist = true
    }else $params.tourist = false
    function initSendParams () {
      return {
        chatId: '',
        messages: [],
        questionId: '',
        regeneration: false,
        similarQuestionId:'',
        query: '',
        tourist: false
      }
    }
    return $params
  }
  /** 暂停回答 */
  const stopAnswer = async () => {
    if (!checkState()) return
    currentQaMap.value?.controller?.abort()
    currentQaMap.value.loading =  false
    state.isLoading = false
    if(!currentQaMap.value.answerId) {
      currentQaMap.value.isTerminate = true
      return
    }
    const { err } = await sseApiStopAnswerByEvents({
      answerId: currentQaMap.value.answerId ?? '',
      content: currentQaMap.value.content ?? '',
      thinkingTime: currentQaMap.value.thinkingTime ?? ''
    })
    if(err) currentQaMap.value.isTerminate = false
    else currentQaMap.value.isTerminate = true
  }
  /** 加载当个历史对话 */
  const getHistoryById = async (chatId: string) => {
    if (!checkState()) return
    state.chatId = chatId
    const { err, data } = await apiGetHistorys({ chatId })
    if(err) return
    data.map((item: any)=> {
      const obj: Record<string,any> = { ...item,chatId, answerList: null,question:item.content, isFinish: true}
      if(item.role === 'user') {
        obj.questionId = item.id
      }
      if(item.answerList && item.answerList.length) {
        for(const answer of item.answerList) {
          const sensitive = answer.suggestion === 'block' ? true : false
          state.qaList.push({...obj,...answer,answerId:answer.id, markType: answer.type, sensitive})
        }
      }else {
        state.qaList.push(obj)
      }
    })
    state.isNewChat = false
  }
  /** 阅读原文 */
  const readFile = (val: any,isNewTarget?:boolean)=> {
    const params = val ?? {}
    state.originParams =  {...params}
    const menuStore = useMenusStore()
    const query:any = encodeURIComponent(JSON.stringify({
      sectionType: params.sectionType,
      fileId:  params.fileId,
      fileName: params.fileName,
      previewUrl: params.previewUrl,
      currentPage: params.currentPage,
      sectionInfo: params.sectionInfo,
      fileStatus: params.fileStatus,
      isSearch: params.isSearch,
      appId: menuStore.appId
    }))
    if(isNewTarget) { // 打开新页面
      window.open(router.resolve({ name: 'ChatRobotFileReader',
        query: {
          query
        }
       }).href, '_blank')
    } else {
      state.isShowQuote = false
      state.isShowQuoteSearch = false
      state.isShowOriginal = true
    }

  }
  /** 重置 */
  const resetChat = () => {
    if (!checkState()) return
    state.selectTag =''
    state.qaList = []
    state.originParams = {}
    state.associateInputList = []
    state.chatId = ''
    state.inputVal = ''
    state.isLoading = false
    state.isShowQuote = false
    state.isShowQuoteSearch = false
    state.disableAllInput = false
    state.isShowOriginal = false
    state.reasonLoading = false
    resetParam()
  }
  /** 重置参数 */
  const resetParam = ()=> {
    state.question = ''
    state.similarQuestionId = ''
    state.questionId = ''
    state.isRegenerate = false
  }
  // 获取点踩理由
  const getDislikeReason = async ()=> {
    if (!checkState() || state.dislikeReason.length || state.reasonLoading) return
    state.reasonLoading = true
    const { err, data } = await apiGetDislikeReasonList()
    state.reasonLoading = false
    if (err) return
    state.dislikeReason = data[0]?.children.map((item: any) => {
      item.checked = false
      return item
    }) ?? []
  }
  // 校验state,currentQaMap值：是否有provide
  function checkState() {
    if (!state || !currentQaMap.value) {
      console.error('use-chat error')
      return false
    }
    return true
  }
  return { sendQuestion, stopAnswer,  getHistoryById, getDislikeReason,  resetChat, resetParam, readFile }
}
