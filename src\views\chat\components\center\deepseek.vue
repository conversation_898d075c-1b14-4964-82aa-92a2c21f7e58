<template>
  <div class="deepseek">
    <header @click="toggleVisible">
      <template v-if="visible">
        <DownOutlined />
        隐藏思考过程
      </template>
      <template v-else>
        <RightOutlined />
        显示思考过程
      </template>
    </header>

    <a-steps v-if="visible" direction="vertical" :current="currenStep">
      <template v-for="item in props.item.steps" :key="item.stepId">
        <a-step @click="stepClick">
          <template #title>
            <a-collapse v-if="item.items" v-model:activeKey="activeKeys" expand-icon-position="end">
              <a-collapse-panel :key="item.stepId" :header="item.stepName">
                <div class="step-item-list">
                  <template v-for="(a, aIndex) in item.items" :key="aIndex">
                    <div class="step-item">
                      <div class="step-item-detail">
                        <div class="title">{{ a.title }}</div>
                        <div class="content">{{ a.text }}</div>
                      </div>
                      <a-button v-if="a.length" type="primary" :icon="h(SearchOutlined)" shape="round"
                                @click="doClick(a.quote)">
                        {{ a.length }} 个文档
                      </a-button>
                    </div>
                  </template>
                </div>
              </a-collapse-panel>
            </a-collapse>

            <a-collapse v-else expand-icon-position="end">
              <a-collapse-panel :key="item.stepId" :header="item.stepName" :show-arrow="false" class="step-item-none" />
            </a-collapse>
          </template>
        </a-step>
      </template>
    </a-steps>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue'
import { DownOutlined, RightOutlined, SearchOutlined } from '@ant-design/icons-vue'

type Props = {
  item: Record<string, any>
}
const props = withDefaults(defineProps<Props>(), {
  item: () => {
    return {}
  }
})

const emits = defineEmits(['openQuote'])
const visible = ref(true)
const currenStep = ref(0)
const activeKeys = ref<string[]>([])

const stepClick = () => {

}

const toggleVisible = () => {
  visible.value = !visible.value
}

const doClick = (val: Array<string>) => {
  const ids = val.join(',')
  emits('openQuote', ids)
}
</script>

<style scoped lang="scss">
.deepseek {
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: solid 1px var(--main-3);
  border-radius: 8px;
  margin: 0 16px 0 0;
  overflow: hidden;

  .step-item-none {
    :deep(.ant-collapse-content) {
      display: none;
    }
  }

  header {
    background: var(--fill-1);
    height: 48px;
    line-height: 48px;
    font-weight: bold;
    padding: 0 16px;
  }

  :deep(.ant-steps) {
    padding: 0 0 0 16px;

    .ant-steps-item-content {
      padding: 0 0 16px 0;

      .ant-steps-item-title {
        width: 100%;

        .ant-collapse {
          border: none;

          .ant-collapse-item {
            border: none;

            .ant-collapse-header-text {
              font-weight: bold;
            }

            .ant-collapse-content {
              border: none;
            }
          }
        }
      }
    }
  }

  .step-item-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .step-item {
      display: flex;
      gap: 16px;
      align-items: center;
      padding: 12px;
      border-radius: 8px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
      border: 1px solid rgba(19, 60, 232, 0.1);
      transition: all 0.3s ease;
      margin: 0 -16px;

      &:hover {
        transform: translateX(4px);
        border-color: rgba(19, 60, 232, 0.2);
        box-shadow: 0 4px 12px rgba(19, 60, 232, 0.1);
      }

      .step-item-detail {
        flex: 1;

        .title {
          font-weight: bold;
        }

        .content {
          font-style: italic;
          color: var(--text-3);
        }
      }

      .ant-btn {
        background: linear-gradient(135deg, var(--main-6) 0%, var(--main-5) 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(19, 60, 232, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 16px rgba(19, 60, 232, 0.4);
        }

        span {
          display: inline-flex;
        }
      }
    }
  }
}
</style>
